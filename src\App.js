import React, { useState, useEffect, useRef, useCallback } from 'react';
import { db, auth } from './firebase';
import { collection, addDoc, onSnapshot, query, serverTimestamp, updateDoc, doc, getDoc, setDoc, deleteDoc } from 'firebase/firestore';
import { signInAnonymously, signInWithCustomToken, onAuthStateChanged } from 'firebase/auth';
import './App.css';

// Removed react-syntax-highlighter imports due to resolution issues in this environment

// Define global variables that may be injected by the runtime environment
// These are fallback values if the variables are not defined
const __initial_auth_token = window.__initial_auth_token || undefined;
const __app_id = window.__app_id || 'default-app-id';

// Helper function to safely convert Firestore timestamps to Date objects
const getDateFromTimestamp = (timestamp) => {
  if (!timestamp) return null;
  
  if (typeof timestamp.toDate === 'function') {
    // Firestore Timestamp
    return timestamp.toDate();
  } else if (timestamp instanceof Date) {
    // Regular Date object
    return timestamp;
  } else {
    // Try to parse as date string
    const date = new Date(timestamp);
    return isNaN(date.getTime()) ? null : date;
  }
};

// Main App component for the flashcard application
function App() {
  // State variables for user authentication and flashcards
  const [userId, setUserId] = useState(null);
  const [flashcards, setFlashcards] = useState([]); // All flashcards from Firestore
  const [filteredFlashcards, setFilteredFlashcards] = useState([]); // Flashcards after category filter
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [showCreateCardForm, setShowCreateCardForm] = useState(false); // State for toggling create card form
  const [showUploadCsvForm, setShowUploadCsvForm] = useState(false); // State for toggling CSV upload form
  const [uploadMessage, setUploadMessage] = useState(''); // Message for CSV upload success
  const [uploadError, setUploadError] = useState('');     // Message for CSV upload errors
  const [showCalendarModal, setShowCalendarModal] = useState(false); // State for calendar modal visibility
  const [selectedCategory, setSelectedCategory] = useState('All'); // State for selected category filter

  // Settings states
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [settingsLoaded, setSettingsLoaded] = useState(false); // To ensure settings are loaded before saving/using them
  const [fsrsParams, setFsrsParams] = useState({
    easyFactor: 1.5,
    goodFactor: 1.0,
    hardFactor: 0.7,
    againFactor: 0.3,
    initialDifficulty: 5,
    initialStability: 1,
  });
  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState(false); // State for Gemini API loading (Suggest Answer)
  const [isGeneratingExample, setIsGeneratingExample] = useState(false); // State for Gemini API loading (Generate Example)
  const [generatedExample, setGeneratedExample] = useState(''); // State to store generated example
  const [isGeneratingQuestions, setIsGeneratingQuestions] = useState(false); // State for Gemini API loading (Generate Questions)
  // eslint-disable-next-line no-unused-vars
  const [generatedQuestions, setGeneratedQuestions] = useState([]); // State to store generated questions for selection
  const [showGeneratedQuestionsModal, setShowGeneratedQuestionsModal] = useState(false); // State for related questions modal
  const [isExplainingConcept, setIsExplainingConcept] = useState(false); // State for Gemini API loading (Explain Concept)
  const [geminiExplanation, setGeminiExplanation] = useState(''); // State to store generated explanation

  const [showCsvGuide, setShowCsvGuide] = useState(false); // State for toggling CSV guide visibility in settings
  const [showAboutFsrs, setShowAboutFsrs] = useState(false); // State for About FSRS dropdown
  const [showUserInfo, setShowUserInfo] = useState(false); // State for User Info dropdown
  const [showFsrsFactors, setShowFsrsFactors] = useState(true); // State for FSRS Factors dropdown
  const [showGenerationPrompt, setShowGenerationPrompt] = useState(false); // State for Generation Prompt dropdown

  // Edit Card states
  const [isEditingCard, setIsEditingCard] = useState(false); // State for edit mode
  const [editCardData, setEditCardData] = useState(null); // Data for the card being edited
  const [showConfirmDelete, setShowConfirmDelete] = useState(false); // State for delete confirmation

  const [copyFeedback, setCopyFeedback] = useState(''); // State for copy button feedback

  // Refs for new card input fields and file input
  const newCardQuestionRef = useRef(null);
  const newCardAnswerRef = useRef(null);
  const newCardCategoryRef = useRef(null); // Ref for new card category input
  const newCardAdditionalInfoRef = useRef(null); // New ref for additional info
  const fileInputRef = useRef(null); // Ref for the CSV file input
  const [selectedUploadFiles, setSelectedUploadFiles] = useState([]); // State to hold selected file objects (array)

  // Refs for edit card inputs
  const editQuestionRef = useRef(null);
  const editAnswerRef = useRef(null);
  const editCategoryRef = useRef(null);
  const editAdditionalInfoRef = useRef(null);

  // Set up authentication listener
  useEffect(() => {
    console.log("Setting up auth listener...", { auth, db });
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log("Auth state changed:", user ? user.uid : "No user");
      if (user) {
        setUserId(user.uid);
      } else {
        try {
          console.log("Attempting anonymous sign in...");
          if (typeof __initial_auth_token !== 'undefined') {
            await signInWithCustomToken(auth, __initial_auth_token);
          } else {
            await signInAnonymously(auth);
          }
        } catch (error) {
          console.error("Firebase authentication error:", error);
        }
      }
      setIsAuthReady(true);
    });

    return () => unsubscribe();
  }, []);

  // Fetch/Set FSRS settings and dark mode from Firestore
  useEffect(() => {
    console.log("Settings effect triggered:", { db: !!db, userId, isAuthReady });
    if (db && userId && isAuthReady) {
      const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
      const settingsDocRef = doc(db, `/artifacts/${appId}/users/${userId}/settings`, 'app_settings');

      const fetchSettings = async () => {
        try {
          console.log("Fetching settings...");
          const docSnap = await getDoc(settingsDocRef);
          if (docSnap.exists()) {
            const fetchedSettings = docSnap.data();
            console.log("Settings found:", fetchedSettings);
            setFsrsParams(prev => ({ ...prev, ...fetchedSettings.fsrsParams }));
            setIsDarkMode(fetchedSettings.isDarkMode || false);
          } else {
            console.log("No settings found, creating default settings...");
            // Set initial settings if they don't exist
            await setDoc(settingsDocRef, {
              fsrsParams: fsrsParams, // use default initial state
              isDarkMode: false
            });
          }
          console.log("Settings loaded successfully");
          setSettingsLoaded(true); // Mark settings as loaded
        } catch (error) {
          console.error("Error fetching/setting app settings:", error);
          setSettingsLoaded(true); // Still set to true to proceed even if fetch fails
        }
      };
      fetchSettings();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, isAuthReady]);

  // Effect to apply dark mode class to HTML element
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    // Also save the dark mode preference to Firestore if settings are already loaded
    if (db && userId && settingsLoaded) {
      const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
      const settingsDocRef = doc(db, `/artifacts/${appId}/users/${userId}/settings`, 'app_settings');
      updateDoc(settingsDocRef, { isDarkMode: isDarkMode }).catch(error => console.error("Error updating dark mode setting:", error));
    }
  }, [isDarkMode, userId, settingsLoaded]);

  // Function to update FSRS parameters and save to Firestore
  const updateFsrsParameter = (paramName, value) => {
    setFsrsParams(prev => {
      const newParams = { ...prev, [paramName]: parseFloat(value) }; // Ensure float
      // Save to Firestore if settings are already loaded
      if (db && userId && settingsLoaded) {
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
        const settingsDocRef = doc(db, `/artifacts/${appId}/users/${userId}/settings`, 'app_settings');
        updateDoc(settingsDocRef, { fsrsParams: newParams }).catch(error => console.error("Error updating FSRS settings:", error));
      }
      return newParams;
    });
  };

  // Fetch flashcards from Firestore when DB and auth are ready
  useEffect(() => {
    if (db && auth && userId) {
      const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
      const flashcardsColRef = collection(db, `/artifacts/${appId}/users/${userId}/flashcards`);
      const q = query(flashcardsColRef);

      const unsubscribe = onSnapshot(q, (snapshot) => {
        const cards = [];
        snapshot.forEach(doc => {
          cards.push({ id: doc.id, ...doc.data() });
        });
        cards.sort((a, b) => (a.nextReview?.toMillis() || 0) - (b.nextReview?.toMillis() || 0));
        setFlashcards(cards);
        if (currentCardIndex >= cards.length && cards.length > 0) {
          setCurrentCardIndex(0);
        }
      }, (error) => {
        console.error("Error fetching flashcards:", error);
      });

      return () => unsubscribe();
    }
  }, [userId, currentCardIndex]);

  // Effect to filter flashcards whenever `flashcards` or `selectedCategory` changes
  useEffect(() => {
    if (selectedCategory === 'All') {
      setFilteredFlashcards(flashcards);
    } else {
      setFilteredFlashcards(flashcards.filter(card => card.category === selectedCategory));
    }
    setCurrentCardIndex(0);
    setShowAnswer(false);
  }, [flashcards, selectedCategory]);

  const nextCard = useCallback(() => {
    setShowAnswer(false);
    setGeneratedExample(''); // Clear generated example on next card
    setGeneratedQuestions([]); // Clear generated questions on next card
    setGeminiExplanation(''); // Clear generated explanation on next card
    setShowGeneratedQuestionsModal(false); // Close modal on card change
    setCurrentCardIndex((prevIndex) => (prevIndex + 1) % filteredFlashcards.length);
  }, [filteredFlashcards.length]);

  const prevCard = () => {
    setShowAnswer(false);
    setGeneratedExample(''); // Clear generated example on prev card
    setGeneratedQuestions([]); // Clear generated questions on prev card
    setGeminiExplanation(''); // Clear generated explanation on prev card
    setShowGeneratedQuestionsModal(false); // Close modal on card change
    setCurrentCardIndex((prevIndex) =>
      (prevIndex - 1 + filteredFlashcards.length) % filteredFlashcards.length
    );
  };

  const handleAddCard = async (e) => {
    e.preventDefault();

    const question = newCardQuestionRef.current.value.trim();
    const answer = newCardAnswerRef.current.value.trim();
    const category = newCardCategoryRef.current.value.trim() || 'Uncategorized';
    const additionalInfo = newCardAdditionalInfoRef.current.value.trim() || null; // Capture additional info

    if (!question || !answer) {
      console.log("Please enter both question and answer.");
      return;
    }

    if (db && userId) {
      try {
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
        const now = new Date();

        await addDoc(collection(db, `/artifacts/${appId}/users/${userId}/flashcards`), {
          question,
          answer,
          category,
          additional_info: additionalInfo, // Save additional info
          difficulty: fsrsParams.initialDifficulty, // Use initial difficulty from settings
          stability: fsrsParams.initialStability,   // Use initial stability from settings
          lastReview: serverTimestamp(),
          nextReview: now,
          createdAt: serverTimestamp(),
        });
        console.log("Flashcard added successfully!");
        newCardQuestionRef.current.value = '';
        newCardAnswerRef.current.value = '';
        newCardCategoryRef.current.value = '';
        newCardAdditionalInfoRef.current.value = ''; // Clear additional info
        setShowCreateCardForm(false);
      } catch (error) {
        console.error("Error adding flashcard:", error);
      }
    } else {
      console.log("Database not initialized or user not authenticated.");
    }
  };

  /**
   * Applies a simplified FSRS-like algorithm to update flashcard parameters.
   * Uses customizable factors from fsrsParams state.
   */
  const reviewCard = useCallback(async (quality, card) => {
    if (!db || !userId || !card || !settingsLoaded) return; // Ensure settings are loaded

    const lastReviewDate = getDateFromTimestamp(card.lastReview) || new Date();
    const nextReviewDate = getDateFromTimestamp(card.nextReview) || new Date();
    const now = new Date();
    const elapsedDays = (now.getTime() - lastReviewDate.getTime()) / (1000 * 60 * 60 * 24);

    let newDifficulty = card.difficulty;
    let newStability = card.stability;
    let newIntervalDays = 0;

    switch (quality) {
      case 1: // Again
        newDifficulty = Math.min(10, newDifficulty + 1.5);
        newStability = Math.max(0.1, newStability * fsrsParams.againFactor);
        newIntervalDays = 0.5;
        break;
      case 2: // Hard
        newDifficulty = Math.min(10, newDifficulty + 0.8);
        newStability = Math.max(0.5, newStability * fsrsParams.hardFactor);
        newIntervalDays = newStability;
        break;
      case 3: // Good
        newDifficulty = Math.max(1, newDifficulty - 0.5);
        newStability = newStability * (1 + 0.1 * elapsedDays / newDifficulty) * fsrsParams.goodFactor;
        newIntervalDays = newStability;
        break;
      case 4: // Easy
        newDifficulty = Math.max(1, newDifficulty - 1.0);
        newStability = newStability * (1 + 0.2 * elapsedDays / newDifficulty) * fsrsParams.easyFactor;
        newIntervalDays = newStability * 1.5;
        break;
      default:
        console.warn("Invalid quality rating:", quality);
        return;
    }

    newStability = Math.max(0.1, newStability);
    newDifficulty = Math.max(1, Math.min(10, newDifficulty));

    const nextReview = new Date(now.getTime() + newIntervalDays * 24 * 60 * 60 * 1000);

    const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
    try {
      await updateDoc(doc(db, `/artifacts/${appId}/users/${userId}/flashcards`, card.id), {
        difficulty: newDifficulty,
        stability: newStability,
        lastReview: serverTimestamp(),
        nextReview: nextReview,
      });
      console.log(`Card ${card.id} reviewed with quality ${quality}. Next review in ${newIntervalDays.toFixed(1)} days.`);
      nextCard();
    } catch (error) {
      console.error("Error updating flashcard:", error);
    }
  }, [userId, settingsLoaded, fsrsParams, nextCard]);

  /**
   * Parses a CSV string into an array of flashcard objects.
   * Handles quoted fields, including multiline content and commas within quotes.
   * Expected format: number,category,question,answer,additional_info
   */
  const parseCSV = (csvString) => {
    // Split into lines, filter empty ones, and slice to ignore the header row
    const lines = csvString.split(/\r?\n/).filter(line => line.trim() !== '').slice(1);
    const cards = [];

    lines.forEach((line, index) => {
      let inQuote = false;
      let fieldBuffer = '';
      const rowFields = [];

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          if (i + 1 < line.length && line[i + 1] === '"') {
            fieldBuffer += '"';
            i++;
          } else {
            inQuote = !inQuote;
          }
        } else if (char === ',' && !inQuote) {
          rowFields.push(fieldBuffer);
          fieldBuffer = '';
        } else {
          fieldBuffer += char;
        }
      }
      rowFields.push(fieldBuffer);

      while (rowFields.length < 5) {
        rowFields.push('');
      }

      const csvNumber = rowFields[0].trim();
      const category = rowFields[1].trim() || 'Uncategorized';
      const question = rowFields[2].trim();
      const answer = rowFields[3].trim();
      const additional_info = rowFields[4].trim();

      if (question && answer) {
        cards.push({
          csvNumber: csvNumber || null,
          category,
          question,
          answer,
          additional_info: additional_info || null,
        });
      } else {
        console.warn(`Skipping row ${index + 1} due to missing Question or Answer: "${line}"`);
      }
    });

    return cards;
  };

  // Handles the CSV file selection (for multiple files)
  const handleFileSelect = (event) => {
    setSelectedUploadFiles(Array.from(event.target.files)); // Store FileList as an array
    setUploadMessage(''); // Clear previous messages
    setUploadError('');
  };

  // Handles the actual CSV upload when the button is clicked
  const handleUploadButtonClick = async () => {
    if (selectedUploadFiles.length === 0) {
      setUploadError('Please select at least one file first.');
      return;
    }

    setUploadMessage('Processing CSV files...');
    setUploadError('');

    let totalCardsAdded = 0;
    let totalFilesProcessed = 0;
    let errorsFound = false;

    for (const file of selectedUploadFiles) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        setUploadError((prev) => prev + `Invalid file type for ${file.name}. Only CSV files are supported.\n`);
        errorsFound = true;
        continue;
      }

      try {
        const csvContent = await new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target.result);
          reader.onerror = (e) => reject(e);
          reader.readAsText(file, 'UTF-8');
        });

        const parsedCards = parseCSV(csvContent);

        if (parsedCards.length === 0) {
          setUploadError((prev) => prev + `No valid flashcards found in ${file.name}.\n`);
          errorsFound = true;
          continue;
        }

        for (const cardData of parsedCards) {
          if (db && userId) {
            const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
            const now = new Date();

            await addDoc(collection(db, `/artifacts/${appId}/users/${userId}/flashcards`), {
              question: cardData.question,
              answer: cardData.answer,
              category: cardData.category,
              additional_info: cardData.additional_info,
              csvNumber: cardData.csvNumber,
              difficulty: fsrsParams.initialDifficulty,
              stability: fsrsParams.initialStability,
              lastReview: serverTimestamp(),
              nextReview: now,
              createdAt: serverTimestamp(),
            });
            totalCardsAdded++;
          }
        }
        totalFilesProcessed++;
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        setUploadError((prev) => prev + `Failed to process ${file.name}: ${error.message || error}.\n`);
        errorsFound = true;
      }
    }

    if (!errorsFound && totalFilesProcessed > 0) {
      setUploadMessage(`Successfully added ${totalCardsAdded} flashcard(s) from ${totalFilesProcessed} file(s).`);
    } else if (errorsFound && totalFilesProcessed > 0) {
        setUploadMessage(`Processed ${totalFilesProcessed} file(s). Added ${totalCardsAdded} cards. Some errors occurred.`);
    } else if (errorsFound && totalFilesProcessed === 0) {
        setUploadMessage("No files were successfully processed.");
    }
    
    setSelectedUploadFiles([]); // Clear selected files after upload attempt
    if (fileInputRef.current) fileInputRef.current.value = ''; // Clear file input visual
  };

  // Function to handle answer suggestion using Gemini API
  const handleSuggestAnswer = async () => {
    const question = newCardQuestionRef.current.value.trim();
    if (!question) {
      console.log("Please enter a question to get a suggestion.");
      return;
    }

    setIsGeneratingAnswer(true);
    try {
      let chatHistory = [];
      const prompt = `Provide a concise and accurate answer for the Java flashcard question: "${question}"`;
      chatHistory.push({ role: "user", parts: [{ text: prompt }] });

      const payload = { contents: chatHistory };
      const apiKey = ""; // Canvas will inject API key here
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const text = result.candidates[0].content.parts[0].text;
        newCardAnswerRef.current.value = text.trim(); // Populate the answer field
      } else {
        console.error("Gemini API response did not contain expected text:", result);
        // Optionally display an error message to the user
      }
    } catch (error) {
      console.error("Error calling Gemini API:", error);
      // Optionally display an error message to the user
    } finally {
      setIsGeneratingAnswer(false);
    }
  };

  // Function to handle generating code example using Gemini API
  const handleGenerateExample = async (question, answer) => {
    if (!question || !answer) {
      console.log("Question and Answer are required to generate an example.");
      return;
    }

    setIsGeneratingExample(true);
    setGeneratedExample(''); // Clear previous example

    try {
      let chatHistory = [];
      const prompt = `Provide a concise Java code example or a clear real-world scenario that illustrates the concept from this flashcard. 
      Question: "${question}"
      Answer: "${answer}"
      Focus on providing a direct, illustrative example. Output only the example, no conversational text.`;
      chatHistory.push({ role: "user", parts: [{ text: prompt }] });

      const payload = { contents: chatHistory };
      const apiKey = ""; // Canvas will inject API key here
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const text = result.candidates[0].content.parts[0].text;
        setGeneratedExample(text.trim()); // Store the generated example
      } else {
        console.error("Gemini API response for example did not contain expected text:", result);
        setGeneratedExample("Could not generate example. Please try again.");
      }
    } catch (error) {
      console.error("Error calling Gemini API for example:", error);
      setGeneratedExample("Error generating example. Check console for details.");
    } finally {
      setIsGeneratingExample(false);
    }
  };

  // Function to handle generating related questions using Gemini API
  const handleGenerateQuestions = async (question, answer) => {
    if (!question || !answer) {
      console.log("Question and Answer are required to generate related questions.");
      return;
    }

    setIsGeneratingQuestions(true);
    setGeneratedQuestions([]); // Clear previous questions before generating

    try {
      let chatHistory = [];
      const prompt = `Based on the following Java flashcard:\nQuestion: "${question}"\nAnswer: "${answer}"\n\nGenerate 3-5 concise, related follow-up questions to test deeper understanding. Provide them as a numbered list.`;
      chatHistory.push({ role: "user", parts: [{ text: prompt }] });

      const payload = { contents: chatHistory };
      const apiKey = ""; // Canvas will inject API key here
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const text = result.candidates[0].content.parts[0].text;
        // Simple parsing for numbered list, adjust as needed for robust parsing
        const questionsArray = text.split('\n').filter(line => line.match(/^\d+\./)).map(line => ({
            id: crypto.randomUUID(), // Unique ID for each generated question
            text: line.replace(/^\d+\.\s*/, '').trim(),
            selected: true // Default to selected
        }));
        setGeneratedQuestions(questionsArray);
        setShowGeneratedQuestionsModal(true); // Open the modal
      } else {
        console.error("Gemini API response for questions did not contain expected text:", result);
        setGeneratedQuestions([{ id: 'error', text: "Could not generate questions. Please try again.", selected: false }]);
        setShowGeneratedQuestionsModal(true); // Open modal even on error to show message
      }
    } catch (error) {
      console.error("Error calling Gemini API for questions:", error);
      setGeneratedQuestions([{ id: 'error', text: "Error generating questions. Check console for details.", selected: false }]);
      setShowGeneratedQuestionsModal(true); // Open modal even on error to show message
    } finally {
      setIsGeneratingQuestions(false);
    }
  };




  // Function to handle explaining a concept using Gemini API
  const handleExplainConcept = async (question, answer) => {
    if (!question || !answer) {
      console.log("Question and Answer are required to explain the concept.");
      return;
    }

    setIsExplainingConcept(true);
    setGeminiExplanation(''); // Clear previous explanation

    try {
      let chatHistory = [];
      const prompt = `Explain the core concept or provide more context for the following Java flashcard:\nQuestion: "${question}"\nAnswer: "${answer}"\n\nKeep the explanation concise and beginner-friendly.`;
      chatHistory.push({ role: "user", parts: [{ text: prompt }] });

      const payload = { contents: chatHistory };
      const apiKey = ""; // Canvas will inject API key here
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const text = result.candidates[0].content.parts[0].text;
        setGeminiExplanation(text.trim()); // Store the generated explanation
      } else {
        console.error("Gemini API response for explanation did not contain expected text:", result);
        setGeminiExplanation("Could not generate explanation. Please try again.");
      }
    } catch (error) {
      console.error("Error calling Gemini API for explanation:", error);
      setGeminiExplanation("Error generating explanation. Check console for details.");
    } finally {
      setIsExplainingConcept(false);
    }
  };


  // Function to enter edit mode for a card
  const handleEditCard = (card) => {
    setEditCardData({ ...card }); // Copy card data to edit state
    setGeneratedQuestions([]); // Clear any old generated questions when opening edit from button
    setIsEditingCard(true);
  };

  // Function to save changes to a card
  const handleSaveCardChanges = async () => {
    if (!db || !userId || !editCardData) return;

    const updatedQuestion = editQuestionRef.current.value.trim();
    const updatedAnswer = editAnswerRef.current.value.trim();
    const updatedCategory = editCategoryRef.current.value.trim() || 'Uncategorized';
    const updatedAdditionalInfo = editAdditionalInfoRef.current.value.trim() || null;

    if (!updatedQuestion || !updatedAnswer) {
      alert("Question and Answer cannot be empty."); // Using alert for simplicity, replace with custom modal for production
      return;
    }

    const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
    try {
      await updateDoc(doc(db, `/artifacts/${appId}/users/${userId}/flashcards`, editCardData.id), {
        question: updatedQuestion,
        answer: updatedAnswer,
        category: updatedCategory,
        additional_info: updatedAdditionalInfo,
      });
      console.log(`Card ${editCardData.id} updated successfully!`);
      setIsEditingCard(false); // Exit edit mode
      setEditCardData(null); // Clear edit data
      setGeneratedQuestions([]); // Clear generated questions after saving
    } catch (error) {
      console.error("Error updating flashcard:", error);
    }
  };

  // Function to delete a card
  const handleDeleteCard = async () => {
    if (!db || !userId || !editCardData) return;

    // Show confirmation dialog before deleting
    setShowConfirmDelete(true);
  };

  // Confirmed delete action
  const confirmDelete = async () => {
    setShowConfirmDelete(false); // Close confirmation modal
    if (!db || !editCardData?.id) return;

    const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
    try {
      await deleteDoc(doc(db, `/artifacts/${appId}/users/${userId}/flashcards`, editCardData.id));
      console.log(`Card ${editCardData.id} deleted successfully!`);
      setIsEditingCard(false); // Exit edit mode
      setEditCardData(null); // Clear edit data
      setGeneratedQuestions([]); // Clear generated questions after deleting
      // Navigate to the next card or default view if current card was deleted
      if (filteredFlashcards.length > 0) {
        setCurrentCardIndex(0); // Reset to first card
      } else {
        setCurrentCardIndex(0); // No cards left
      }
    } catch (error) {
      console.error("Error deleting flashcard:", error);
    }
  };

  // Cancel delete action
  const cancelDelete = () => {
    setShowConfirmDelete(false);
  };

  const handleCopyPrompt = () => {
    // Uses the Clipboard API for modern browsers
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(generationPromptContent.trim())
        .then(() => {
          setCopyFeedback('Copied!');
          setTimeout(() => setCopyFeedback(''), 2000); // Clear feedback after 2 seconds
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
          setCopyFeedback('Failed to copy.');
          setTimeout(() => setCopyFeedback(''), 2000);
        });
    } else {
      // Fallback for older browsers (e.g., execCommand)
      // Note: execCommand is deprecated, but useful for broader compatibility in some limited environments
      const textArea = document.createElement("textarea");
      textArea.value = generationPromptContent.trim();
      textArea.style.position = "fixed";  // Avoid scrolling to bottom
      textArea.style.left = "-9999px"; // Hide element
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setCopyFeedback('Copied!');
        setTimeout(() => setCopyFeedback(''), 2000);
      } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        setCopyFeedback('Failed to copy (fallback).');
        setTimeout(() => setCopyFeedback(''), 2000);
      }
      document.body.removeChild(textArea);
    }
  };


  // Calculate reviewed and yet-to-review counts for FILTERED cards
  const now = new Date(); // Use current time for more precise "due" calculation

  // Cards that are due for review (nextReview is now or in the past, or not set for new cards)
  const yetToReviewCards = filteredFlashcards.filter(card => {
    const nextReviewDate = getDateFromTimestamp(card.nextReview);
    // A card is due if it has no nextReview date (new card) or if its nextReview date is now or in the past
    return !nextReviewDate || nextReviewDate <= now;
  });

  // Cards that have been reviewed and are not yet due again (nextReview is in the future)
  const reviewedCards = filteredFlashcards.filter(card => {
    const nextReviewDate = getDateFromTimestamp(card.nextReview);
    // A card is reviewed if it has a nextReview date AND that date is in the future
    return nextReviewDate && nextReviewDate > now;
  });

  const totalCardsCount = filteredFlashcards.length;
  const reviewedCount = reviewedCards.length;
  const toReviewCount = yetToReviewCards.length;


  // Function to get daily due counts for the next 30 days (for ALL flashcards, not just filtered)
  const getDailyDueCounts = () => {
    const dailyCounts = {};
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const dateString = date.toISOString().split('T')[0];
      dailyCounts[dateString] = 0;
    }

    flashcards.forEach(card => {
      const nextReviewDate = getDateFromTimestamp(card.nextReview);
      
      if (nextReviewDate && !isNaN(nextReviewDate.getTime())) {
        const cardDueDate = new Date(nextReviewDate);
        cardDueDate.setHours(0, 0, 0, 0);

        if (cardDueDate <= today) {
          const todayString = today.toISOString().split('T')[0];
          dailyCounts[todayString] = (dailyCounts[todayString] || 0) + 1;
        } else {
          const futureDateString = cardDueDate.toISOString().split('T')[0];
          if (dailyCounts.hasOwnProperty(futureDateString)) {
            dailyCounts[futureDateString] = (dailyCounts[futureDateString] || 0) + 1;
          }
        }
      } else {
        const todayString = today.toISOString().split('T')[0];
        dailyCounts[todayString] = (dailyCounts[todayString] || 0) + 1;
      }
    });

    return dailyCounts;
  };

  // Function to get the next review date for a card
  const getNextReviewDate = (card) => {
    if (!card.nextReview) return null;
    
    if (typeof card.nextReview.toDate === 'function') {
      return card.nextReview.toDate();
    } else if (card.nextReview instanceof Date) {
      return card.nextReview;
    } else {
      const date = new Date(card.nextReview);
      return isNaN(date.getTime()) ? null : date;
    }
  };

  // Function to format the next review date for display
  const formatNextReviewDate = (card) => {
    const nextReviewDate = getDateFromTimestamp(card.nextReview);
    if (!nextReviewDate) return 'Not scheduled';
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const cardDate = new Date(nextReviewDate);
    cardDate.setHours(0, 0, 0, 0);
    
    if (cardDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (cardDate.getTime() === tomorrow.getTime()) {
      return 'Tomorrow';
    } else {
      return cardDate.toLocaleDateString();
    }
  };

  const dailyDueCounts = getDailyDueCounts();

  // Get unique categories for the dropdown
  const uniqueCategories = ['All', ...new Set(flashcards.map(card => card.category || 'Uncategorized'))].sort();

  // Effect to handle keyboard events for navigation and review
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Only trigger if no modals or forms are open and there are filtered flashcards
      const isStudyViewActive = !showCreateCardForm && !showUploadCsvForm && !showCalendarModal && !showSettingsModal && !isEditingCard && !showGeneratedQuestionsModal;
      if (!isStudyViewActive || filteredFlashcards.length === 0) {
        return;
      }

      if (event.code === 'Space') {
        event.preventDefault(); // Prevent scrolling down the page
        setShowAnswer(prev => !prev); // Toggle the answer visibility
        setGeneratedExample(''); // Clear generated example when flipping
        setGeneratedQuestions([]); // Clear generated questions when flipping
        setGeminiExplanation(''); // Clear generated explanation when flipping
      } else if (showAnswer) { // Only allow review shortcuts if the answer is shown
        if (event.key === 'a' || event.key === 'A') {
          event.preventDefault();
          reviewCard(1, filteredFlashcards[currentCardIndex]);
        } else if (event.key === 'h' || event.key === 'H') {
          event.preventDefault();
          reviewCard(2, filteredFlashcards[currentCardIndex]);
        } else if (event.key === 'g' || event.key === 'G') {
          event.preventDefault();
          reviewCard(3, filteredFlashcards[currentCardIndex]);
        } else if (event.key === 'e' || event.key === 'E') {
          event.preventDefault();
          reviewCard(4, filteredFlashcards[currentCardIndex]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Cleanup the event listener when the component unmounts or dependencies change
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    showCreateCardForm,
    showUploadCsvForm,
    showCalendarModal,
    showSettingsModal,
    isEditingCard, // Added dependency for edit mode
    showGeneratedQuestionsModal, // Added dependency
    filteredFlashcards, // Dependency for filteredFlashcards[currentCardIndex]
    currentCardIndex,   // Dependency for filteredFlashcards[currentCardIndex]
    showAnswer,         // Dependency for conditional review shortcuts
    reviewCard          // Dependency for reviewCard function
  ]);

  // Content for the CSV Upload Guide in Settings
  const csvUploadGuideContent = `
### Understanding the CSV Format and Escape Characters

Your flashcard application expects a specific CSV format to correctly parse the data. Here's a breakdown of each field and how to handle special characters:

**Format:** \`number,category,question,answer,additional_info\`

1.  **\`number\` (Optional):**
    * This field is for an arbitrary numerical identifier.
    * **If you don't provide a number, leave this field completely empty.** Do not put \`""\` or \` \` (a space).
    * Example (empty number): \`,,Spring Security,What is CSRF protection?,...\`

2.  **\`category\` (Optional):**
    * This field allows you to group your flashcards.
    * **If you don't provide a category, leave this field completely empty.** It will default to 'Uncategorized' in the app.
    * Example (empty category): \`,,What is a binary tree?,...\`

3.  **\`question\` (Required):**
    * The question for your flashcard. This field cannot be empty.

4.  **\`answer\` (Required):**
    * The answer to your flashcard. This field cannot be empty.

5.  **\`additional_info\` (Optional):**
    * This field is for any extra notes, context, or code examples related to the flashcard.
    * **If you don't provide additional info, leave this field completely empty.**

#### Handling Special Characters within Fields

The most critical aspect of CSV is correctly handling content that contains delimiters (commas), line breaks, or quotation marks within a field's data.

* **Commas (\`,\`) within a field:**
    * If a field's content contains a comma, the **entire field must be enclosed in double quotes (\`"\`)\`.**
    * Example: \`,,What are the three core principles of OOP?,"Encapsulation, Inheritance, Polymorphism",...\`

* **Newline characters (line breaks) within a field:**
    * If a field's content spans multiple lines, the **entire field must be enclosed in double quotes (\`"\`)\`.** The newlines within the quotes will be preserved.
    * Example: \`,,Explain @Transactional annotation,"@Transactional annotation provides:\\n1. Transaction management\\n2. Rollback capabilities\\n3. Isolation levels",...\`
    * *Note:* The \`\\n\` here represents a newline character.

* **Double Quotes (\`"\`) within a quoted field:**
    * If a field is enclosed in double quotes (because it contains commas or newlines), and the field * itself* contains a double quote, that **internal double quote must be escaped by preceding it with another double quote (\`""\`)\`.**
    * Example: \`,,What does ""immutable"" mean?,"It means the object's state cannot be changed after it is created.",...\`

#### Example of a Complete CSV File

\`\`\`
number,category,question,answer,additional_info
,Spring Security,What is CSRF protection?,"CSRF protection prevents unauthorized commands from being transmitted from a user's browser to a web application. It uses a token to ensure requests are legitimate.","More details on synchronizer token pattern, often involves a hidden token."
10,Spring Data,Explain @Transactional annotation,"@Transactional annotation provides:\\n1. Transaction management\\n2. Rollback capabilities\\n3. Isolation levels","Useful for ensuring data consistency in database operations."
,Java Basics,What is the difference between \`==\` and \`.equals()\`?,"\`==\` compares object references (memory addresses) for primitive types or if two variables refer to the exact same object instance.
\`.equals()\` compares the actual content/value of objects, as defined by the class's implementation (e.g., String, Integer classes override it to compare content).","Primitive types always use \`==\`."
20,Algorithms,What is Big O notation?,"Big O notation describes the upper bound of the growth rate of an algorithm's running time or space requirements in terms of the input size. It helps classify algorithms according to how their run time or space requirements grow as the input size grows.","Common Big O notations: O(1), O(log n), O(n), O(n log n), O(n^2), O(2^n), O(n!)."
\`\`\`
`;

  // Content for the Prompt for Generating Flashcards
  const generationPromptContent = `
Please generate 20 flashcards about [Subject].
For each flashcard, provide the following information in CSV format:
number,category,question,answer,additional_info

Constraints:
- Number is optional; if omitted, leave it empty.
- Category is optional; if omitted, default to 'Uncategorized'.
- Question and Answer are required and should not be empty.
- Use quotes for multiline content or content that contains commas.
- Ensure the CSV content is UTF-8 encoded.
- Additional_info is optional; if omitted, leave it empty.

Example with multiline answer and additional info:
,Spring Security,What is CSRF protection?,"CSRF protection prevents unauthorized commands from being transmitted from a user's browser to a web application. It uses a token to ensure requests are legitimate.",More details on synchronizer token pattern.

Example with no number, category, or additional_info:
,,What is a binary tree?,A tree data structure in which each node has at most two children, which are referred to as the left child and the right child.
`;

  // Sort flashcards by next review date
  useEffect(() => {
    if (flashcards.length > 0) {
      const sortedCards = [...flashcards].sort((a, b) => {
        const dateA = getNextReviewDate(a);
        const dateB = getNextReviewDate(b);
        
        if (!dateA && !dateB) return 0;
        if (!dateA) return 1;
        if (!dateB) return -1;
        
        return dateA.getTime() - dateB.getTime();
      });
      
      setFlashcards(sortedCards);
    }
  }, [flashcards]);

  // Update the renderCard function
  const renderCard = () => {
    if (filteredFlashcards.length === 0) {
      return (
        <div className="card text-center">
          <p className="text-gray-600 dark:text-gray-400">No flashcards available.</p>
          <button 
            className="btn-primary mt-4"
            onClick={() => setShowCreateCardForm(true)}
          >
            Create Your First Flashcard
          </button>
        </div>
      );
    }

    const currentCard = filteredFlashcards[currentCardIndex];
    return (
      <div className="card">
        <div className="flex justify-between items-center mb-4">
          <span className="category-tag">{currentCard.category}</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Next Review: {formatNextReviewDate(currentCard)}
          </span>
        </div>
        
        <div className="flashcard-content">
          <div className="flashcard-question">
            {currentCard.question}
          </div>
          {showAnswer && (
            <div className="flashcard-answer mt-4">
              {currentCard.answer}
              {currentCard.additional_info && (
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {currentCard.additional_info}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="nav-controls mt-6">
          {!showAnswer ? (
            <button 
              className="btn-primary w-full"
              onClick={() => setShowAnswer(true)}
            >
              Show Answer
            </button>
          ) : (
            <div className="flex gap-2 w-full">
              <button 
                className="btn-danger flex-1"
                onClick={() => reviewCard(1, currentCard)}
              >
                Again
              </button>
              <button 
                className="btn-secondary flex-1"
                onClick={() => reviewCard(2, currentCard)}
              >
                Hard
              </button>
              <button 
                className="btn-primary flex-1"
                onClick={() => reviewCard(3, currentCard)}
              >
                Good
              </button>
              <button 
                className="btn-secondary flex-1"
                onClick={() => reviewCard(4, currentCard)}
              >
                Easy
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Update the loading state
  if (!isAuthReady || !settingsLoaded) {
    return (
      <div className="loading">
        <div className="loading-spinner"></div>
        <div className="mt-4 text-center">
          <p className="text-gray-600 dark:text-gray-400">Loading application...</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
            Auth Ready: {isAuthReady ? 'Yes' : 'No'} | Settings Loaded: {settingsLoaded ? 'Yes' : 'No'}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            User ID: {userId || 'None'} | DB: {db ? 'Connected' : 'Not Connected'}
          </p>
          <div className="mt-4 space-y-2">
            <button
              className="btn-primary w-full"
              onClick={() => {
                setIsAuthReady(true);
                setSettingsLoaded(true);
                setUserId('test-user');
              }}
            >
              Skip Loading (Debug)
            </button>
            <button
              className="btn-secondary w-full"
              onClick={() => {
                setIsAuthReady(true);
                setSettingsLoaded(true);
                setUserId('test-user');
                setFlashcards([
                  {
                    id: '1',
                    question: 'What is React?',
                    answer: 'React is a JavaScript library for building user interfaces.',
                    category: 'React',
                    additional_info: 'Created by Facebook',
                    difficulty: 5,
                    stability: 1,
                    lastReview: new Date(),
                    nextReview: new Date()
                  },
                  {
                    id: '2',
                    question: 'What is useState?',
                    answer: 'useState is a React Hook that lets you add state to functional components.',
                    category: 'React',
                    additional_info: 'Returns an array with current state and setter function',
                    difficulty: 5,
                    stability: 1,
                    lastReview: new Date(),
                    nextReview: new Date()
                  }
                ]);
              }}
            >
              Skip Loading + Add Sample Cards
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Flashcard App</h1>
        <div className="flex gap-2">
          <button 
            className="btn-secondary"
            onClick={() => setShowCreateCardForm(true)}
          >
            Create Card
          </button>
          <button 
            className="btn-primary"
            onClick={() => setShowSettingsModal(true)}
          >
            Settings
          </button>
        </div>
      </div>

      {renderCard()}

      {/* Navigation buttons */}
      <div className="nav-controls">
        <button 
          className="btn-secondary"
          onClick={prevCard}
          disabled={currentCardIndex === 0}
        >
          Previous
        </button>
        <button 
          className="btn-primary"
          onClick={nextCard}
          disabled={currentCardIndex === filteredFlashcards.length - 1}
        >
          Next
        </button>
      </div>

      {/* Modals */}
      {showCreateCardForm && (
        <div className="modal">
          <div className="modal-content">
            <h2 className="text-xl font-bold mb-4">Create New Flashcard</h2>
            <form onSubmit={handleAddCard}>
              <input
                type="text"
                placeholder="Question"
                ref={newCardQuestionRef}
                required
              />
              <textarea
                placeholder="Answer"
                ref={newCardAnswerRef}
                required
              />
              <input
                type="text"
                placeholder="Category (optional)"
                ref={newCardCategoryRef}
              />
              <textarea
                placeholder="Additional Info (optional)"
                ref={newCardAdditionalInfoRef}
              />
              <div className="flex gap-2">
                <button type="submit" className="btn-primary flex-1">
                  Create
                </button>
                <button 
                  type="button" 
                  className="btn-secondary flex-1"
                  onClick={() => setShowCreateCardForm(false)}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showSettingsModal && (
        <div className="modal">
          <div className="modal-content">
            <h2 className="text-xl font-bold mb-4">Settings</h2>
            <div className="settings-panel">
              <div className="settings-group">
                <h3>Dark Mode</h3>
                <button 
                  className={`btn-${isDarkMode ? 'primary' : 'secondary'}`}
                  onClick={() => setIsDarkMode(!isDarkMode)}
                >
                  {isDarkMode ? 'Disable Dark Mode' : 'Enable Dark Mode'}
                </button>
              </div>
              {/* Add more settings groups as needed */}
            </div>
            <button 
              className="btn-primary w-full mt-4"
              onClick={() => setShowSettingsModal(false)}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
