/* Base styles and variables */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #10b981;
  --secondary-hover: #059669;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --background-light: #ffffff;
  --background-dark: #1f2937;
  --text-light: #1f2937;
  --text-dark: #f3f4f6;
  --card-bg-light: #ffffff;
  --card-bg-dark: #374151;
  --border-light: #e5e7eb;
  --border-dark: #4b5563;
  --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-dark: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Dark mode styles */
.dark {
  --background: var(--background-dark);
  --text: var(--text-dark);
  --card-bg: var(--card-bg-dark);
  --border: var(--border-dark);
  --shadow: var(--shadow-dark);
}

/* Light mode styles */
:root {
  --background: var(--background-light);
  --text: var(--text-light);
  --card-bg: var(--card-bg-light);
  --border: var(--border-light);
  --shadow: var(--shadow-light);
}

/* Global styles */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background);
  color: var(--text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* App container */
.App {
  min-height: 100vh;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Card styles */
.card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  margin: 1rem 0;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--border);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 12px -1px rgba(0, 0, 0, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.06);
}

/* Button styles */
button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-hover);
}

/* Form styles */
input, textarea, select {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border);
  background-color: var(--card-bg);
  color: var(--text);
  margin-bottom: 1rem;
  transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Navigation and controls */
.nav-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

/* Flashcard content */
.flashcard-content {
  font-size: 1.25rem;
  line-height: 1.75;
  margin: 1rem 0;
}

.flashcard-question {
  font-weight: 600;
  margin-bottom: 1rem;
}

.flashcard-answer {
  color: var(--text);
  opacity: 0.9;
}

/* Category tags */
.category-tag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--primary-color);
  color: white;
  margin-right: 0.5rem;
}

/* Loading state */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-spinner {
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 640px) {
  .App {
    padding: 1rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .nav-controls {
    flex-direction: column;
  }
  
  button {
    width: 100%;
  }
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-bg);
  padding: 2rem;
  border-radius: 1rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Settings panel */
.settings-panel {
  background-color: var(--card-bg);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 1rem;
  border: 1px solid var(--border);
}

.settings-group {
  margin-bottom: 1.5rem;
}

.settings-group h3 {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

/* Progress indicators */
.progress-bar {
  height: 0.5rem;
  background-color: var(--border);
  border-radius: 9999px;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

/* Tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem;
  background-color: var(--card-bg);
  color: var(--text);
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  box-shadow: var(--shadow);
  z-index: 10;
}
