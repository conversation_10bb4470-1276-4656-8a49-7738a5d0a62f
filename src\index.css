/* Modern CSS Framework - Comprehensive Styling */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* CSS Variables for consistent theming */
:root {
  --primary-blue: #3b82f6;
  --primary-blue-dark: #2563eb;
  --primary-blue-light: #60a5fa;
  --secondary-purple: #8b5cf6;
  --secondary-purple-dark: #7c3aed;
  --accent-green: #10b981;
  --accent-green-dark: #059669;
  --accent-red: #ef4444;
  --accent-yellow: #f59e0b;
  --accent-orange: #f97316;
  --accent-pink: #ec4899;
  --accent-teal: #14b8a6;
  --accent-indigo: #6366f1;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

/* Dark mode variables */
.dark {
  --gray-50: #111827;
  --gray-100: #1f2937;
  --gray-200: #374151;
  --gray-300: #4b5563;
  --gray-400: #6b7280;
  --gray-500: #9ca3af;
  --gray-600: #d1d5db;
  --gray-700: #e5e7eb;
  --gray-800: #f3f4f6;
  --gray-900: #f9fafb;
}

/* Layout utilities */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* Spacing utilities */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-2\.5 { padding-top: 0.625rem; padding-bottom: 0.625rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-4 { margin-left: 1rem; }
.mr-4 { margin-right: 1rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

/* Sizing utilities */
.w-full { width: 100%; }
.w-3\/4 { width: 75%; }
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-5xl { max-width: 64rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.max-h-20 { max-height: 5rem; }
.max-h-60 { max-height: 15rem; }
.max-h-full { max-height: 100%; }
.min-h-\[42rem\] { min-height: 42rem; }
.block { display: block; }

/* Typography utilities */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; }
.font-inter { font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif; }
.tracking-tight { letter-spacing: -0.025em; }
.leading-tight { line-height: 1.25; }

/* Color utilities */
.text-white { color: #ffffff; }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }
.text-blue-700 { color: #1d4ed8; }
.text-green-600 { color: #16a34a; }
.text-red-600 { color: #dc2626; }
.text-indigo-700 { color: #4338ca; }
.text-purple-700 { color: #7c3aed; }
.text-transparent { color: transparent; }

/* Background utilities */
.bg-white { background-color: #ffffff; }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-gray-200 { background-color: var(--gray-200); }
.bg-gray-300 { background-color: var(--gray-300); }
.bg-gray-400 { background-color: var(--gray-400); }
.bg-gray-600 { background-color: var(--gray-600); }
.bg-gray-700 { background-color: var(--gray-700); }
.bg-gray-800 { background-color: var(--gray-800); }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-200 { background-color: #bfdbfe; }
.bg-blue-500 { background-color: var(--primary-blue); }
.bg-blue-600 { background-color: var(--primary-blue-dark); }
.bg-blue-800 { background-color: #1e40af; }
.bg-green-200 { background-color: #bbf7d0; }
.bg-green-500 { background-color: var(--accent-green); }
.bg-green-600 { background-color: var(--accent-green-dark); }
.bg-green-800 { background-color: #166534; }
.bg-red-600 { background-color: var(--accent-red); }
.bg-red-700 { background-color: #b91c1c; }
.bg-red-800 { background-color: #991b1b; }
.bg-yellow-600 { background-color: var(--accent-yellow); }
.bg-yellow-700 { background-color: #a16207; }
.bg-yellow-800 { background-color: #854d0e; }
.bg-purple-200 { background-color: #e9d5ff; }
.bg-purple-600 { background-color: var(--secondary-purple); }
.bg-purple-700 { background-color: var(--secondary-purple-dark); }
.bg-indigo-200 { background-color: #c7d2fe; }
.bg-indigo-600 { background-color: var(--accent-indigo); }
.bg-indigo-800 { background-color: #3730a3; }
.bg-orange-600 { background-color: var(--accent-orange); }
.bg-orange-700 { background-color: #ea580c; }
.bg-pink-600 { background-color: var(--accent-pink); }
.bg-pink-700 { background-color: #be185d; }
.bg-teal-600 { background-color: var(--accent-teal); }
.bg-teal-700 { background-color: #0f766e; }

/* Gradient backgrounds */
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-blue-100 { --tw-gradient-from: #dbeafe; --tw-gradient-to: rgb(219 234 254 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-blue-200 { --tw-gradient-to: #bfdbfe; }
.from-blue-600 { --tw-gradient-from: var(--primary-blue-dark); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-purple-700 { --tw-gradient-to: var(--secondary-purple-dark); }
.from-blue-400 { --tw-gradient-from: var(--primary-blue-light); --tw-gradient-to: rgb(96 165 250 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-purple-500 { --tw-gradient-to: var(--secondary-purple); }

/* Background opacity */
.bg-opacity-50 { --tw-bg-opacity: 0.5; }
.bg-opacity-90 { --tw-bg-opacity: 0.9; }
.bg-black { background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1)); }

/* Clip path */
.bg-clip-text { background-clip: text; -webkit-background-clip: text; }

/* Border utilities */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-0 { border-width: 0px; }
.border-blue-300 { border-color: #93c5fd; }
.border-gray-200 { border-color: var(--gray-200); }
.border-gray-600 { border-color: var(--gray-600); }
.border-transparent { border-color: transparent; }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05); }

/* Interactive utilities */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.appearance-none { appearance: none; }
.resize-y { resize: vertical; }
.overflow-auto { overflow: auto; }
.overflow-y-auto { overflow-y: auto; }
.backface-hidden { backface-visibility: hidden; }

/* Focus utilities */
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-4:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-blue-400:focus { --tw-ring-color: #60a5fa; }
.focus\:ring-blue-300:focus { --tw-ring-color: #93c5fd; }
.focus\:ring-blue-500:focus { --tw-ring-color: var(--primary-blue); }
.focus\:ring-green-300:focus { --tw-ring-color: #86efac; }
.focus\:ring-red-300:focus { --tw-ring-color: #fca5a5; }
.focus\:ring-yellow-300:focus { --tw-ring-color: #fde047; }
.focus\:ring-purple-300:focus { --tw-ring-color: #d8b4fe; }
.focus\:ring-gray-300:focus { --tw-ring-color: var(--gray-300); }
.focus\:ring-indigo-300:focus { --tw-ring-color: #a5b4fc; }
.focus\:ring-orange-300:focus { --tw-ring-color: #fdba74; }
.focus\:ring-pink-300:focus { --tw-ring-color: #f9a8d4; }
.focus\:ring-teal-300:focus { --tw-ring-color: #5eead4; }
.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }
.focus\:border-transparent:focus { border-color: transparent; }
.focus\:border-blue-500:focus { border-color: var(--primary-blue); }

/* Hover effects */
.hover\:bg-blue-300:hover { background-color: #93c5fd; }
.hover\:bg-blue-600:hover { background-color: var(--primary-blue-dark); }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-green-300:hover { background-color: #86efac; }
.hover\:bg-green-600:hover { background-color: var(--accent-green-dark); }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:bg-gray-300:hover { background-color: var(--gray-300); }
.hover\:bg-gray-400:hover { background-color: var(--gray-400); }
.hover\:bg-gray-500:hover { background-color: var(--gray-500); }
.hover\:bg-gray-600:hover { background-color: var(--gray-600); }
.hover\:bg-gray-700:hover { background-color: var(--gray-700); }
.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:bg-yellow-700:hover { background-color: #a16207; }
.hover\:bg-purple-700:hover { background-color: var(--secondary-purple-dark); }
.hover\:bg-indigo-300:hover { background-color: #c7d2fe; }
.hover\:bg-indigo-700:hover { background-color: #4338ca; }
.hover\:bg-orange-700:hover { background-color: #ea580c; }
.hover\:bg-pink-700:hover { background-color: #be185d; }
.hover\:bg-teal-700:hover { background-color: #0f766e; }
.hover\:text-gray-700:hover { color: var(--gray-700); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }

/* Active effects */
.active\:scale-90:active { transform: scale(0.9); }
.active\:scale-95:active { transform: scale(0.95); }
.active\:shadow-inner:active { box-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05); }

/* Transform utilities */
.transform { transform: var(--tw-transform); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }

/* Transition utilities */
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* List utilities */
.list-disc { list-style-type: disc; }
.list-inside { list-style-position: inside; }

/* File input utilities */
.file\:mr-4::file-selector-button { margin-right: 1rem; }
.file\:py-2::file-selector-button { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.file\:px-4::file-selector-button { padding-left: 1rem; padding-right: 1rem; }
.file\:rounded-full::file-selector-button { border-radius: 9999px; }
.file\:border-0::file-selector-button { border-width: 0px; }
.file\:text-sm::file-selector-button { font-size: 0.875rem; line-height: 1.25rem; }
.file\:font-semibold::file-selector-button { font-weight: 600; }
.file\:bg-blue-50::file-selector-button { background-color: #eff6ff; }
.file\:text-blue-700::file-selector-button { color: #1d4ed8; }
.hover\:file\:bg-blue-100:hover::file-selector-button { background-color: #dbeafe; }

/* Screen reader utilities */
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border-width: 0; }

/* Dark mode support */
.dark .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--gray-800), var(--gray-900)); }
.dark .text-gray-100 { color: var(--gray-100); }
.dark .text-gray-200 { color: var(--gray-200); }
.dark .text-gray-300 { color: var(--gray-300); }
.dark .text-gray-400 { color: var(--gray-400); }
.dark .bg-gray-600 { background-color: var(--gray-600); }
.dark .bg-gray-700 { background-color: var(--gray-700); }
.dark .bg-gray-800 { background-color: var(--gray-800); }
.dark .bg-gray-900 { background-color: var(--gray-900); }
.dark .border-gray-600 { border-color: var(--gray-600); }
.dark .hover\:bg-gray-500:hover { background-color: var(--gray-500); }
.dark .hover\:bg-gray-600:hover { background-color: var(--gray-600); }
.dark .hover\:bg-gray-700:hover { background-color: var(--gray-700); }
.dark .hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.dark .hover\:bg-green-700:hover { background-color: #15803d; }
.dark .focus\:ring-blue-400:focus { --tw-ring-color: #60a5fa; }
.dark .focus\:border-blue-400:focus { border-color: #60a5fa; }
.dark .file\:bg-blue-800::file-selector-button { background-color: #1e40af; }
.dark .file\:text-blue-100::file-selector-button { color: #dbeafe; }
.dark .hover\:file\:bg-blue-700:hover::file-selector-button { background-color: #1d4ed8; }
.dark .from-gray-800 { --tw-gradient-from: var(--gray-800); --tw-gradient-to: rgb(31 41 55 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.dark .to-gray-900 { --tw-gradient-to: var(--gray-900); }
.dark .from-blue-700 { --tw-gradient-from: #1d4ed8; --tw-gradient-to: rgb(29 78 216 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.dark .to-purple-800 { --tw-gradient-to: #6b21a8; }

body {
  margin: 0;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Custom card flip animation */
.card-flip {
  perspective: 1000px;
}

.card-flip-inner {
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.card-flip.flipped .card-flip-inner {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  backface-visibility: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
}

.card-back {
  transform: rotateY(180deg);
}
